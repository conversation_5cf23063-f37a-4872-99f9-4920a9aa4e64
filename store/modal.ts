import { createSlice, PayloadAction } from '@reduxjs/toolkit';

/**
 * @description 此全局状态用来统一管理，3d场景中需要打开弹窗testApiWindow 组件中打开情况，通常这种情况跨了多层级组件，直接放到redux方便使用
 */
interface IModalState {
  placePetModalConfig: {
    isOpen: boolean;
    positionTag: string;
  };
  // isPlacePetModalOpen: boolean;
  petSynthesisModalConfig: {
    isOpen: boolean;
    confirmCallback?: (...args: any) => void;
  };
  toolSynthesisModalConfig: {
    confirmCallback?: (...args: any) => void;
    isOpen: boolean;
  };
  petSubmitChainModalConfig: {
    isOpen: boolean;
    configData?: {
      mergeData?: {
        ids: string[];
        slotRecordId?: string;
      };
      generateData?: {
        userItemId?: string;
        positionTag?: string;
      };
    };
    confirmCallback?: (...args: any) => void;
  };
  petBedDescModalConfig: {
    isOpen: boolean;
    confirmCallback?: (...args: any) => void;
  };
  petDescModalOpenConfig: {
    isOpen: boolean;
    confirmCallback?: (...args: any) => void;
  };
  petFusionModalConfig: {
    isOpen: boolean;
    configData: {
      slotRecordId: string;
    };
    confirmCallback?: (...args: any) => void;
  };
}

const initialState: IModalState = {
  placePetModalConfig: {
    isOpen: false,
    positionTag: '',
  },
  petSynthesisModalConfig: {
    isOpen: false,
    confirmCallback: () => false,
  },
  toolSynthesisModalConfig: {
    confirmCallback: () => false,
    isOpen: false,
  },
  petSubmitChainModalConfig: {
    confirmCallback: () => false,
    configData: undefined,
    isOpen: false,
  },
  petBedDescModalConfig: {
    confirmCallback: () => false,
    isOpen: false,
  },
  petDescModalOpenConfig: {
    confirmCallback: () => false,
    isOpen: false,
  },
  petFusionModalConfig: {
    confirmCallback: () => false,
    configData: {
      slotRecordId: '',
    },
    isOpen: false,
  },
};

export const ModalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    updateModalState(state, action: PayloadAction<Partial<IModalState>>) {
      const {
        placePetModalConfig,
        petSynthesisModalConfig,
        toolSynthesisModalConfig,
        petSubmitChainModalConfig,
        petBedDescModalConfig,
        petDescModalOpenConfig,
        petFusionModalConfig,
      } = action.payload;
      state.placePetModalConfig = placePetModalConfig ?? state.placePetModalConfig;
      state.petSynthesisModalConfig = petSynthesisModalConfig ?? state.petSynthesisModalConfig;
      state.petSubmitChainModalConfig =
        petSubmitChainModalConfig ?? state.petSubmitChainModalConfig;
      state.petBedDescModalConfig = petBedDescModalConfig ?? state.petBedDescModalConfig;
      state.petDescModalOpenConfig = petDescModalOpenConfig ?? state.petDescModalOpenConfig;
      state.petFusionModalConfig = petFusionModalConfig ?? state.petFusionModalConfig;
      if (toolSynthesisModalConfig) {
        state.toolSynthesisModalConfig = {
          ...state.toolSynthesisModalConfig,
          ...toolSynthesisModalConfig,
        };
      }
    },
    resetModalState(state) {
      state.placePetModalConfig = {
        isOpen: false,
        positionTag: '',
      };
      state.petSynthesisModalConfig = {
        confirmCallback: () => false,
        isOpen: false,
      };
      state.toolSynthesisModalConfig = {
        confirmCallback: () => false,
        isOpen: false,
      };
      state.petSubmitChainModalConfig = {
        confirmCallback: () => false,
        configData: undefined,
        isOpen: false,
      };
      state.petBedDescModalConfig = {
        confirmCallback: () => false,
        isOpen: false,
      };
      state.petDescModalOpenConfig = {
        confirmCallback: () => false,
        isOpen: false,
      };
      state.petFusionModalConfig = {
        confirmCallback: () => false,
        isOpen: false,
        configData: {
          slotRecordId: '',
        },
      };
    },
  },
});
export const { updateModalState, resetModalState } = ModalSlice.actions;
export default ModalSlice.reducer;
