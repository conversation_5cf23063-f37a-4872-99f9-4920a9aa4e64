<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3D粒子系统演示</title>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }

      body {
          overflow: hidden;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
          color: #e6e6e6;
      }

      #container {
          position: absolute;
          width: 100%;
          height: 100%;
      }

      #ui {
          position: absolute;
          bottom: 20px;
          left: 20px;
          z-index: 100;
          background: rgba(0, 0, 0, 0.7);
          padding: 15px;
          border-radius: 10px;
          backdrop-filter: blur(5px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          max-width: 300px;
      }

      h1 {
          font-size: 16px;
          margin-bottom: 15px;
          color: #4cc9f0;
          text-transform: uppercase;
          letter-spacing: 1px;
      }

      .control-group {
          margin-bottom: 15px;
      }

      label {
          display: block;
          margin-bottom: 5px;
          font-size: 14px;
      }

      input[type="range"] {
          width: 100%;
          height: 5px;
          -webkit-appearance: none;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 5px;
          outline: none;
      }

      input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #4cc9f0;
          cursor: pointer;
      }

      .btn {
          background: #4cc9f0;
          color: #0c1a2d;
          border: none;
          padding: 8px 15px;
          border-radius: 5px;
          cursor: pointer;
          font-weight: bold;
          transition: all 0.3s ease;
          margin-right: 10px;
      }

      .btn:hover {
          background: #2b9ac7;
          transform: translateY(-2px);
      }

      #info {
          position: absolute;
          top: 20px;
          right: 20px;
          background: rgba(0, 0, 0, 0.7);
          padding: 10px 15px;
          border-radius: 10px;
          font-size: 14px;
          backdrop-filter: blur(5px);
          border: 1px solid rgba(255, 255, 255, 0.1);
      }

      #title {
          position: absolute;
          top: 20px;
          left: 20px;
          font-size: 28px;
          font-weight: 700;
          color: #fff;
          text-shadow: 0 0 10px rgba(76, 201, 240, 0.5);
      }
  </style>
</head>
<body>
<div id="container"></div>
<div id="title">3D粒子系统</div>
<div id="info">粒子数: 1000 | FPS: 60</div>

<div id="ui">
  <h1>粒子控制系统</h1>

  <div class="control-group">
    <label for="rotationSpeed">旋转速度</label>
    <input type="range" id="rotationSpeed" min="0" max="2" step="0.1" value="0.5">
  </div>

  <div class="control-group">
    <label for="particleSize">粒子大小</label>
    <input type="range" id="particleSize" min="0.1" max="3" step="0.1" value="1">
  </div>

  <div class="control-group">
    <label for="spread">分布范围</label>
    <input type="range" id="spread" min="10" max="200" step="10" value="100">
  </div>

  <div class="control-group">
    <button class="btn" id="resetBtn">重置粒子</button>
    <button class="btn" id="colorBtn">改变颜色</button>
  </div>
</div>

<script>
  // 初始化变量
  let scene, camera, renderer, instancedMesh, root;
  let particleCount = 1000;
  let frameCount = 0;
  let startTime = Date.now();
  let fps = 60;
  let rotationSpeed = 0.5;
  let particleSize = 1;
  let spread = 100;

  // 初始化Three.js场景
  function init() {
    // 创建场景
    scene = new THREE.Scene();
    root = new THREE.Object3D();
    scene.add(root);
    scene.background = new THREE.Color(0x0a0a1a);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 150;

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    document.getElementById('container').appendChild(renderer.domElement);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x444444);
    scene.add(ambientLight);

    // 添加点光源
    const pointLight = new THREE.PointLight(0x4cc9f0, 1, 1000);
    pointLight.position.set(50, 50, 50);
    scene.add(pointLight);

    // 创建粒子系统
    createParticleSystem();

    // 添加窗口调整事件监听
    window.addEventListener('resize', onWindowResize);

    // 添加UI控制
    document.getElementById('rotationSpeed').addEventListener('input', function(e) {
      rotationSpeed = parseFloat(e.target.value);
    });

    document.getElementById('particleSize').addEventListener('input', function(e) {
      particleSize = parseFloat(e.target.value);
      updateParticleSize();
    });

    document.getElementById('spread').addEventListener('input', function(e) {
      spread = parseFloat(e.target.value);
      resetParticles();
    });

    document.getElementById('resetBtn').addEventListener('click', resetParticles);

    document.getElementById('colorBtn').addEventListener('click', function() {
      const colors = [0x4cc9f0, 0xf72585, 0x7209b7, 0x3a0ca3, 0x4361ee, 0xf9c74f];
      const randomColor = colors[Math.floor(Math.random() * colors.length)];
      instancedMesh.material.color.setHex(randomColor);
    });

    // 开始动画
    animate();
  }

  // 创建粒子系统
  function createParticleSystem() {
    const DEFAULT_TEXTUREURL = '/particles/dot.png';
    const particleGeometry = new THREE.PlaneGeometry(1, 1);

    // 创建程序化纹理作为备用方案
    const createFallbackTexture = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 64;
      canvas.height = 64;
      const context = canvas.getContext('2d');

      // 创建径向渐变圆点
      const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);
      gradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
      gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.5)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      context.fillStyle = gradient;
      context.fillRect(0, 0, 64, 64);

      const texture = new THREE.CanvasTexture(canvas);
      texture.needsUpdate = true;
      return texture;
    };

    // 尝试加载纹理，失败时使用程序化纹理
    const textureLoader = new THREE.TextureLoader();
    let texture;

    try {
      texture = textureLoader.load(
        DEFAULT_TEXTUREURL,
        // 成功回调
        function(loadedTexture) {
          console.log('Particle texture loaded successfully');
        },
        // 进度回调
        undefined,
        // 错误回调
        function(error) {
          console.warn('Failed to load particle texture, using fallback:', error);
          // 使用程序化纹理替换
          particleMaterial.map = createFallbackTexture();
          particleMaterial.needsUpdate = true;
        }
      );
    } catch (error) {
      console.warn('Error loading texture, using fallback immediately');
      texture = createFallbackTexture();
    }

    const particleMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      color: 0x4cc9f0,
      transparent: true,
      side: THREE.DoubleSide,
      depthWrite: false,
      blending: THREE.AdditiveBlending
    });

    instancedMesh = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);
    // instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);

    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();

    for (let i = 0; i < particleCount; i++) {
      // 设置位置
      position.set(
        Math.random() * spread - spread / 2,
        Math.random() * spread - spread / 2,
        Math.random() * spread - spread / 2,
      );

      // 设置旋转（使用四元数）
      const rotationX = Math.random() * Math.PI * 2;
      const rotationY = Math.random() * Math.PI * 2;
      const rotationZ = Math.random() * Math.PI * 2;

      // 从欧拉角创建四元数
      quaternion.setFromEuler(new THREE.Euler(rotationX, rotationY, rotationZ));

      // 设置缩放
      const scalar = Math.random() * 1.5 + 0.5;
      scale.set(scalar, scalar, scalar);

      // 组合矩阵
      matrix.compose(position, quaternion, scale);
      instancedMesh.setMatrixAt(i, matrix);
    }

    instancedMesh.instanceMatrix.needsUpdate = true;
    scene.add(instancedMesh);
  }

  // 更新粒子大小
  function updateParticleSize() {
    const matrix = new THREE.Matrix4();
    const scale = new THREE.Vector3();

    for (let i = 0; i < particleCount; i++) {
      instancedMesh.getMatrixAt(i, matrix);
      matrix.decompose(new THREE.Vector3(), new THREE.Quaternion(), scale);

      // 应用新的缩放
      scale.set(particleSize, particleSize, particleSize);
      matrix.compose(
        new THREE.Vector3().setFromMatrixPosition(matrix),
        new THREE.Quaternion().setFromRotationMatrix(matrix),
        scale,
      );

      instancedMesh.setMatrixAt(i, matrix);
    }

    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  // 更新粒子朝向摄像机
  function updateParticleBillboard() {
    const matrix = new THREE.Matrix4();
    const position = new THREE.Vector3();
    const quaternion = new THREE.Quaternion();
    const scale = new THREE.Vector3();
    const lookAtMatrix = new THREE.Matrix4();

    for (let i = 0; i < particleCount; i++) {
      // 获取当前粒子的矩阵
      instancedMesh.getMatrixAt(i, matrix);
      matrix.decompose(position, quaternion, scale);

      // 计算朝向摄像机的四元数
      const direction = new THREE.Vector3();
      direction.subVectors(camera.position, position).normalize();

      // 创建朝向摄像机的四元数
      lookAtMatrix.lookAt(position, camera.position, camera.up);
      quaternion.setFromRotationMatrix(lookAtMatrix);

      // 重新组合矩阵
      matrix.compose(position, quaternion, scale);
      instancedMesh.setMatrixAt(i, matrix);
    }

    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  // 重置粒子
  function resetParticles() {
    const matrix = new THREE.Matrix4();
    const position = new THREE.Vector3();
    const quaternion = new THREE.Quaternion();
    const scale = new THREE.Vector3();

    for (let i = 0; i < particleCount; i++) {
      // 设置位置
      position.set(
        Math.random() * spread - spread / 2,
        Math.random() * spread - spread / 2,
        Math.random() * spread - spread / 2,
      );

      // 设置旋转
      const rotationX = Math.random() * Math.PI * 2;
      const rotationY = Math.random() * Math.PI * 2;
      const rotationZ = Math.random() * Math.PI * 2;

      quaternion.setFromEuler(new THREE.Euler(rotationX, rotationY, rotationZ));

      // 设置缩放
      const scalar = Math.random() * 1.5 + 0.5;
      scale.set(scalar * particleSize, scalar * particleSize, scalar * particleSize);

      // 组合矩阵
      matrix.compose(position, quaternion, scale);
      instancedMesh.setMatrixAt(i, matrix);
    }

    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  // 窗口调整大小时调用
  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }

  // 动画循环
  function animate() {
    requestAnimationFrame(animate);

    // 更新粒子朝向摄像机
    updateParticleBillboard();

    // 旋转粒子系统
    instancedMesh.rotation.x += 0.001 * rotationSpeed;
    instancedMesh.rotation.y += 0.002 * rotationSpeed;

    // 更新FPS计数
    frameCount++;
    const now = Date.now();
    if (now - startTime >= 1000) {
      fps = frameCount;
      frameCount = 0;
      startTime = now;
      document.getElementById('info').textContent = `粒子数: ${particleCount} | FPS: ${fps}`;
    }

    renderer.render(scene, camera);
  }

  // 初始化应用
  init();
</script>
</body>
</html>