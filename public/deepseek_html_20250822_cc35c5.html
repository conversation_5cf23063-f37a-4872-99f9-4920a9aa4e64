<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态彩色粒子系统</title>
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #e6e6e6;
        }
        
        #container {
            position: absolute;
            width: 100%;
            height: 100%;
        }
        
        #ui {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 300px;
        }
        
        h1 {
            font-size: 16px;
            margin-bottom: 15px;
            color: #4cc9f0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        input[type="range"] {
            width: 100%;
            height: 5px;
            -webkit-appearance: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #4cc9f0;
            cursor: pointer;
        }
        
        .btn {
            background: #4cc9f0;
            color: #0c1a2d;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #2b9ac7;
            transform: translateY(-2px);
        }
        
        #info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #title {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 28px;
            font-weight: 700;
            color: #fff;
            text-shadow: 0 0 10px rgba(76, 201, 240, 0.5);
        }
        
        .color-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            margin: 5px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .color-btn:hover {
            transform: scale(1.2);
            border-color: white;
        }
        
        .color-palette {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="title">动态彩色粒子系统</div>
    <div id="info">粒子数: 500 | FPS: 60</div>
    
    <div id="ui">
        <h1>粒子控制系统</h1>
        
        <div class="control-group">
            <label for="rotationSpeed">旋转速度</label>
            <input type="range" id="rotationSpeed" min="0" max="2" step="0.1" value="0.5">
        </div>
        
        <div class="control-group">
            <label for="particleSize">粒子大小</label>
            <input type="range" id="particleSize" min="0.1" max="3" step="0.1" value="1">
        </div>
        
        <div class="control-group">
            <label for="transparency">整体透明度</label>
            <input type="range" id="transparency" min="0.1" max="1" step="0.1" value="0.8">
        </div>
        
        <div class="control-group">
            <label>颜色模式</label>
            <button class="btn" id="randomColorsBtn">随机颜色</button>
            <button class="btn" id="gradientBtn">渐变颜色</button>
        </div>
        
        <div class="control-group">
            <label>预设颜色</label>
            <div class="color-palette">
                <div class="color-btn" style="background: #4cc9f0;" data-color="#4cc9f0"></div>
                <div class="color-btn" style="background: #f72585;" data-color="#f72585"></div>
                <div class="color-btn" style="background: #7209b7;" data-color="#7209b7"></div>
                <div class="color-btn" style="background: #3a0ca3;" data-color="#3a0ca3"></div>
                <div class="color-btn" style="background: #4361ee;" data-color="#4361ee"></div>
                <div class="color-btn" style="background: #f9c74f;" data-color="#f9c74f"></div>
            </div>
        </div>
        
        <div class="control-group">
            <button class="btn" id="resetBtn">重置粒子</button>
            <button class="btn" id="pulseBtn">脉冲效果</button>
        </div>
    </div>

    <script>
        // 初始化变量
        let scene, camera, renderer, instancedMesh;
        let particleCount = 500;
        let frameCount = 0;
        let startTime = Date.now();
        let fps = 60;
        let rotationSpeed = 0.5;
        let particleSize = 1;
        let transparency = 0.8;
        let colorAttribute;
        let pulseEffect = false;
        
        // 初始化Three.js场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0a0a1a);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 150;
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            document.getElementById('container').appendChild(renderer.domElement);
            
            // 添加环境光
            const ambientLight = new THREE.AmbientLight(0x444444);
            scene.add(ambientLight);
            
            // 添加点光源
            const pointLight = new THREE.PointLight(0x4cc9f0, 1, 1000);
            pointLight.position.set(50, 50, 50);
            scene.add(pointLight);
            
            // 创建粒子系统
            createParticleSystem();
            
            // 添加窗口调整事件监听
            window.addEventListener('resize', onWindowResize);
            
            // 添加UI控制
            document.getElementById('rotationSpeed').addEventListener('input', function(e) {
                rotationSpeed = parseFloat(e.target.value);
            });
            
            document.getElementById('particleSize').addEventListener('input', function(e) {
                particleSize = parseFloat(e.target.value);
                updateParticleSize();
            });
            
            document.getElementById('transparency').addEventListener('input', function(e) {
                transparency = parseFloat(e.target.value);
                updateTransparency();
            });
            
            document.getElementById('randomColorsBtn').addEventListener('click', setRandomColors);
            document.getElementById('gradientBtn').addEventListener('click', setGradientColors);
            document.getElementById('resetBtn').addEventListener('click', resetParticles);
            document.getElementById('pulseBtn').addEventListener('click', togglePulseEffect);
            
            // 颜色按钮事件
            document.querySelectorAll('.color-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const color = this.getAttribute('data-color');
                    setParticleColor(color);
                });
            });
            
            // 开始动画
            animate();
        }
        
        // 创建粒子系统
        function createParticleSystem() {
            const particleGeometry = new THREE.SphereGeometry(0.5, 16, 16);
            
            // 为每个粒子创建颜色属性
            const colors = new Float32Array(particleCount * 3);
            colorAttribute = new THREE.InstancedBufferAttribute(colors, 3);
            particleGeometry.setAttribute('instanceColor', colorAttribute);
            
            const particleMaterial = new THREE.MeshLambertMaterial({
                vertexColors: true,
                transparent: true,
                opacity: transparency
            });
            
            instancedMesh = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);
            instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
            
            const matrix = new THREE.Matrix4();
            const quaternion = new THREE.Quaternion();
            const position = new THREE.Vector3();
            const scale = new THREE.Vector3();
            
            // 初始化粒子位置和颜色
            for (let i = 0; i < particleCount; i++) {
                // 设置位置
                position.set(
                    Math.random() * 200 - 100,
                    Math.random() * 200 - 100,
                    Math.random() * 200 - 100
                );
                
                // 设置旋转
                const rotationX = Math.random() * Math.PI * 2;
                const rotationY = Math.random() * Math.PI * 2;
                const rotationZ = Math.random() * Math.PI * 2;
                
                quaternion.setFromEuler(new THREE.Euler(rotationX, rotationY, rotationZ));
                
                // 设置缩放
                const scalar = Math.random() * 1.5 + 0.5;
                scale.set(scalar, scalar, scalar);
                
                // 组合矩阵
                matrix.compose(position, quaternion, scale);
                instancedMesh.setMatrixAt(i, matrix);
                
                // 设置初始颜色
                setParticleColorByIndex(i, 0x4cc9f0);
            }
            
            instancedMesh.instanceMatrix.needsUpdate = true;
            scene.add(instancedMesh);
        }
        
        // 按索引设置粒子颜色
        function setParticleColorByIndex(index, color) {
            const r = ((color >> 16) & 255) / 255;
            const g = ((color >> 8) & 255) / 255;
            const b = (color & 255) / 255;
            
            colorAttribute.setXYZ(index, r, g, b);
            colorAttribute.needsUpdate = true;
        }
        
        // 设置所有粒子为同一颜色
        function setParticleColor(hexColor) {
            const color = parseInt(hexColor.substring(1), 16);
            const r = ((color >> 16) & 255) / 255;
            const g = ((color >> 8) & 255) / 255;
            const b = (color & 255) / 255;
            
            for (let i = 0; i < particleCount; i++) {
                colorAttribute.setXYZ(i, r, g, b);
            }
            colorAttribute.needsUpdate = true;
        }
        
        // 设置随机颜色
        function setRandomColors() {
            const colors = [0x4cc9f0, 0xf72585, 0x7209b7, 0x3a0ca3, 0x4361ee, 0xf9c74f];
            
            for (let i = 0; i < particleCount; i++) {
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                setParticleColorByIndex(i, randomColor);
            }
        }
        
        // 设置渐变颜色
        function setGradientColors() {
            for (let i = 0; i < particleCount; i++) {
                // 基于位置创建渐变颜色
                const matrix = new THREE.Matrix4();
                instancedMesh.getMatrixAt(i, matrix);
                const position = new THREE.Vector3().setFromMatrixPosition(matrix);
                
                // 归一化位置到0-1范围
                const normalizedX = (position.x + 100) / 200;
                const normalizedY = (position.y + 100) / 200;
                const normalizedZ = (position.z + 100) / 200;
                
                // 基于位置创建RGB颜色
                const r = normalizedX;
                const g = normalizedY;
                const b = normalizedZ;
                
                colorAttribute.setXYZ(i, r, g, b);
            }
            colorAttribute.needsUpdate = true;
        }
        
        // 更新透明度
        function updateTransparency() {
            instancedMesh.material.opacity = transparency;
            instancedMesh.material.needsUpdate = true;
        }
        
        // 更新粒子大小
        function updateParticleSize() {
            const matrix = new THREE.Matrix4();
            const scale = new THREE.Vector3();
            
            for (let i = 0; i < particleCount; i++) {
                instancedMesh.getMatrixAt(i, matrix);
                matrix.decompose(new THREE.Vector3(), new THREE.Quaternion(), scale);
                
                // 应用新的缩放
                scale.set(particleSize, particleSize, particleSize);
                matrix.compose(
                    new THREE.Vector3().setFromMatrixPosition(matrix),
                    new THREE.Quaternion().setFromRotationMatrix(matrix),
                    scale
                );
                
                instancedMesh.setMatrixAt(i, matrix);
            }
            
            instancedMesh.instanceMatrix.needsUpdate = true;
        }
        
        // 重置粒子
        function resetParticles() {
            const matrix = new THREE.Matrix4();
            const position = new THREE.Vector3();
            const quaternion = new THREE.Quaternion();
            const scale = new THREE.Vector3();
            
            for (let i = 0; i < particleCount; i++) {
                // 设置位置
                position.set(
                    Math.random() * 200 - 100,
                    Math.random() * 200 - 100,
                    Math.random() * 200 - 100
                );
                
                // 设置旋转
                const rotationX = Math.random() * Math.PI * 2;
                const rotationY = Math.random() * Math.PI * 2;
                const rotationZ = Math.random() * Math.PI * 2;
                
                quaternion.setFromEuler(new THREE.Euler(rotationX, rotationY, rotationZ));
                
                // 设置缩放
                const scalar = Math.random() * 1.5 + 0.5;
                scale.set(scalar * particleSize, scalar * particleSize, scalar * particleSize);
                
                // 组合矩阵
                matrix.compose(position, quaternion, scale);
                instancedMesh.setMatrixAt(i, matrix);
            }
            
            instancedMesh.instanceMatrix.needsUpdate = true;
        }
        
        // 切换脉冲效果
        function togglePulseEffect() {
            pulseEffect = !pulseEffect;
            document.getElementById('pulseBtn').textContent = pulseEffect ? '停止脉冲' : '脉冲效果';
        }
        
        // 窗口调整大小时调用
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 旋转粒子系统
            instancedMesh.rotation.x += 0.001 * rotationSpeed;
            instancedMesh.rotation.y += 0.002 * rotationSpeed;
            
            // 脉冲效果
            if (pulseEffect) {
                const time = Date.now() * 0.001;
                const pulse = Math.sin(time) * 0.5 + 0.5;
                
                for (let i = 0; i < particleCount; i++) {
                    const matrix = new THREE.Matrix4();
                    instancedMesh.getMatrixAt(i, matrix);
                    
                    const scale = new THREE.Vector3();
                    matrix.decompose(new THREE.Vector3(), new THREE.Quaternion(), scale);
                    
                    // 基于脉冲效果调整透明度
                    const alpha = 0.3 + pulse * 0.7;
                    colorAttribute.setW(i, alpha);
                    
                    // 调整大小
                    const newScale = particleSize * (0.8 + pulse * 0.4);
                    scale.set(newScale, newScale, newScale);
                    
                    matrix.compose(
                        new THREE.Vector3().setFromMatrixPosition(matrix),
                        new THREE.Quaternion().setFromRotationMatrix(matrix),
                        scale
                    );
                    
                    instancedMesh.setMatrixAt(i, matrix);
                }
                
                colorAttribute.needsUpdate = true;
                instancedMesh.instanceMatrix.needsUpdate = true;
            }
            
            // 更新FPS计数
            frameCount++;
            const now = Date.now();
            if (now - startTime >= 1000) {
                fps = Math.round(frameCount);
                frameCount = 0;
                startTime = now;
                document.getElementById('info').textContent = `粒子数: ${particleCount} | FPS: ${fps}`;
            }
            
            renderer.render(scene, camera);
        }
        
        // 初始化应用
        init();
    </script>
</body>
</html>