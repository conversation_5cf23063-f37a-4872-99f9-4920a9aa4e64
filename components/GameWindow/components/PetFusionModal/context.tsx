import { IBagPetList } from '@/constant/type';
import { createTemplateContext } from '@/utils/createTemplateContext';

interface PetFusionContext {
  selectedPetSlot: { first?: IBagPetList; second?: IBagPetList };
  clickPetItemId: string;
  fusionLoading: boolean;
}

const initState: PetFusionContext = {
  selectedPetSlot: {},
  clickPetItemId: '',
  fusionLoading: false,
};

const {
  TemplateContextProvider: PetFusionContextProvider,
  useContextDispatch,
  useContextSelector,
} = createTemplateContext<PetFusionContext>(initState);
export { PetFusionContextProvider, useContextDispatch, useContextSelector };
