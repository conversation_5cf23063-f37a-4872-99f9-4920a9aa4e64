import Modal from '@/components/EditAvatarPage/BasicComponents/Modal';
import { useContextSelector, useContextDispatch } from '../context';
import { RefObject, useEffect, useState } from 'react';
import BasicButton from '@/components/Basic/Button';
import toast from 'react-hot-toast';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateGameState } from '@/store/game';
import useBagInventory from '@/hooks/useBagInventory';
import { petRelease } from '@/server';
import { PetInfoSectionMain } from './PetDetailPanel';
import {
  CloseIcon,
  StyledDialogBody,
  StyledTitle,
  StyledBody,
  StyledFooter,
  ExclamationIcon,
} from '@/components/Basic/ModalContentElement';

interface IReleaseModalProps {
  portalContainerRef?: RefObject<HTMLDivElement>;
}

const PetReleaseModal = ({ portalContainerRef }: IReleaseModalProps) => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const dispatch = useAppDispatch();
  const isReleaseModalOpen = useContextSelector((state) => state.isReleaseModalOpen);
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const contextDispatch = useContextDispatch();
  const { getPetListData } = useBagInventory(false);
  const [loading, setLoading] = useState(false);

  const handlePetRelease = async () => {
    setLoading(true);
    try {
      const res = await petRelease(selectedPetId);
      if (res.data.code === 1) {
        const petIndex = petList.findIndex((item) => item._id === selectedPetId);
        setLoading(false);

        if (petIndex === -1) {
          getPetListData();
        } else {
          const newList = [...petList].filter((item) => item._id !== selectedPetId);
          dispatch(updateGameState({ petList: newList }));
          contextDispatch({
            type: 'UPDATE',
            payload: {
              selectedPetId: '',
              selectedPetInfo:{},
              isExpand: false,
              isReleaseModalOpen: false,
            },
          });
        }
      } else {
        toast.error(res.data.msg);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const onConfirm = () => {
    if (loading) return;

    handlePetRelease();
  };
  const onCloseModal = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isReleaseModalOpen: false,
      },
    });
  };

  useEffect(() => {
    return () => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isReleaseModalOpen: false,
        },
      });
    };
  }, []);

  return (
    <Modal
      visible={isReleaseModalOpen}
      emptyOnly
      portalContainer={portalContainerRef?.current as HTMLElement}
      popupClose={true}
      onClose={onCloseModal}>
      <StyledDialogBody
        onClick={(e) => {
          e.stopPropagation();
        }}>
        <CloseIcon onCloseClick={onCloseModal} />
        <StyledTitle>
          <ExclamationIcon />
          <span>Release</span>
        </StyledTitle>
        <StyledBody>
          <p>May I ask if you have confirmed releasing this pet?</p>
          <PetInfoSectionMain displayOnly />
        </StyledBody>
        <StyledFooter>
          <BasicButton onClick={onConfirm} disabled={loading}>
            <span>Confirm</span>
          </BasicButton>
          <BasicButton $type="cancel" onClick={onCloseModal}>
            <span>Cancel</span>
          </BasicButton>
        </StyledFooter>
      </StyledDialogBody>
    </Modal>
  );
};

export default PetReleaseModal;
