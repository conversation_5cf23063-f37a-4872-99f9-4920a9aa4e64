import Modal from '@/components/EditAvatarPage/BasicComponents/Modal';
import { useContextSelector, useContextDispatch } from '../context';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import BasicButton from '@/components/Basic/Button';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';
import { changePetName } from '@/server';
import toast from 'react-hot-toast';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { updateGameState } from '@/store/game';
import useBagInventory from '@/hooks/useBagInventory';
import {
  CloseIcon,
  StyledDialogBody,
  StyledTitle,
  StyledBody,
  StyledFooter,
} from '@/components/Basic/ModalContentElement';

interface IRenameModalProps {
  portalContainerRef?: RefObject<HTMLDivElement>;
}

const StyledForm = styled.form`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 0.25rem;
`;

const StyledInput = styled.input`
  display: flex;
  width: 17.5rem;
  height: 3rem;
  padding: 0.625rem 0.75rem;
  align-items: center;
  gap: 0.625rem;
  border-radius: 1rem;
  border: 0.125rem solid #cabfab;
  background: #fff;
  box-shadow: 0 0.25rem 0.125rem 0 rgba(0, 0, 0, 0.25) inset;
  outline: none;
  color: #140f08;
  font-family: 'JetBrains Mono';
  font-size: 1.25rem;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.05rem;
  &:active,
  &:focus {
    border-color: #ff8316;
  }
`;

const RenameModal = ({ portalContainerRef }: IRenameModalProps) => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const dispatch = useAppDispatch();
  const isRenameModalOpen = useContextSelector((state) => state.isRenameModalOpen);
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const contextDispatch = useContextDispatch();
  const selectedPetInfo = useContextSelector((state) => state.selectedPetInfo);
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState('');
  const { getPetListData } = useBagInventory(false);

  useEffect(() => {
    if (isRenameModalOpen && selectedPetId) {
      setInputValue(selectedPetInfo?.petName ?? '');
    }
  }, [selectedPetId, isRenameModalOpen, selectedPetInfo?.petName]);

  const handleChangePetName = async () => {
    try {
      const res = await changePetName({ petId: selectedPetId, petName: inputValue.trim() });
      if (res.data.code === 1) {
        const petIndex = petList.findIndex((item) => item._id === selectedPetId);
        if (petIndex === -1) {
          getPetListData();
        } else {
          const newPetList = [...petList];
          const data = res.data.data;
          newPetList[petIndex] = {
            ...newPetList[petIndex],
            petName: data.petName,
            renameCount: newPetList[petIndex].renameCount + 1,
            bagConfigInfo: {
              ...newPetList[petIndex].bagConfigInfo,
              name: data.petName,
            },
          };
          dispatch(updateGameState({ petList: newPetList }));
        }
      } else {
        toast.error(res.data.msg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onConfirm = () => {
    handleChangePetName();
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isRenameModalOpen: false,
      },
    });
  };
  const onCloseModal = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isRenameModalOpen: false,
      },
    });
  };

  useEffect(() => {
    if (isRenameModalOpen) {
      KeyPressUtil.setEnable(false);
    } else {
      KeyPressUtil.setEnable(true);
      setInputValue('');
    }
  }, [isRenameModalOpen]);

  useEffect(() => {
    return () => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isRenameModalOpen: false,
        },
      });
      KeyPressUtil.setEnable(true);
    };
  }, []);

  return (
    <Modal
      visible={isRenameModalOpen}
      emptyOnly
      portalContainer={portalContainerRef?.current as HTMLElement}
      popupClose={false}
      onClose={onCloseModal}>
      <StyledDialogBody
        onClick={(e) => {
          e.stopPropagation();
        }}>
        <CloseIcon onCloseClick={onCloseModal} />
        <StyledTitle>
          <span>Modify</span>
        </StyledTitle>
        <StyledBody>
          <p>{`Change your pet's name(max in 20)`}</p>
          <StyledForm
            name="petRenameForm"
            onSubmit={(e) => {
              e.preventDefault();
            }}>
            <StyledInput
              type="text"
              placeholder="Enter your pet name"
              ref={inputRef}
              maxLength={20}
              minLength={1}
              autoComplete="off"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
            />
          </StyledForm>
        </StyledBody>
        <StyledFooter>
          <BasicButton $type="cancel" onClick={onCloseModal}>
            <span>Cancel</span>
          </BasicButton>
          <BasicButton onClick={onConfirm}>
            <span>Confirm</span>
          </BasicButton>
        </StyledFooter>
      </StyledDialogBody>
    </Modal>
  );
};

export default RenameModal;
