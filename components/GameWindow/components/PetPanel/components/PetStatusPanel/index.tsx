import { useAppSelector } from '@/hooks/useStore';
import { useCallback, useMemo, useRef, useState } from 'react';
import { IBagPetList, SCENE_TYPE } from '@/constant/type';
import { PetStatus } from '@/constant/enum';
import Image from 'next/image';
import { AnimatePresence } from 'framer-motion';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import { useContextDispatch, useContextSelector } from '../../context';
import {
  PetStatusItem,
  PetStatusList,
  PetStatusImageBox,
  StyledCursor,
  PetStamina,
  PetImageStaminBox,
  CursorBtnContainer,
  PetOperationList,
  PetOperationItem,
  StyledOperationSvg,
  PetOperationListWrapper,
} from './style';
import { changePetStatus } from '@/server';
import toast from 'react-hot-toast';
import useClickAway from '@/hooks/useClickAway';
import useUpdatePetListItem from '@/hooks/useUpdatePetListItem';
import {
  basicOperationArr,
  featureOperationConfigArr,
  staminaColorConfigArr,
} from '../../constant';
import { StatusTag } from '../PetDetailPanel/style';
import styled from 'styled-components';
import useBagInventory from '@/hooks/useBagInventory';

const OperationList = ({
  petItem,
  closeList = () => false,
}: {
  petItem: IBagPetList;
  closeList: () => void;
}) => {
  const { updateGameStatePetItemData } = useUpdatePetListItem();
  const contextDispatch = useContextDispatch();
  const isChangePetStatusLoading = useContextSelector((state) => state.isChangePetStatusLoading);
  const { sceneType } = useAppSelector((state) => state.AppReducer);
  const { getMaterialListData } = useBagInventory(false, false);

  const handleChangePetStatus = async (petStatus: PetStatus) => {
    if (isChangePetStatusLoading) return;
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isChangePetStatusLoading: true,
      },
    });
    try {
      const res = await changePetStatus({ petId: petItem._id, petStatus });
      if (res.data.code === 1) {
        const newPetData = res.data.data;
        updateGameStatePetItemData(newPetData);
      } else {
        toast.error(res.data.msg);
      }
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    } catch (error) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          isChangePetStatusLoading: false,
        },
      });
    }
  };
  const isInIsland = sceneType === SCENE_TYPE.Island;

  const feedOperation = [
    {
      name: 'Feed',
      iconId: 'petFeed',
      action: '',
      panelColorConfig: {
        borderColor: '',
        color: '',
      },
      panelDisplay: 'Feed',
    },
  ];

  const operationConfigArr = useMemo(() => {
    const featureList = petItem.featureInfos.map((item) => item.feature);
    const isFullStaminal = petItem.stamina === petItem.currentStamina;

    const list = isInIsland
      ? featureOperationConfigArr.filter((item) => featureList.includes(item.action))
      : [];
    const arr = basicOperationArr.toSpliced(1, 0, ...list);
    if (!isFullStaminal) {
      arr.push(...(feedOperation as any));
    }
    return arr;
  }, [isInIsland, petItem]);

  const handleFeed = () => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        isPetFeedModalOpen: true,
      },
    });
    getMaterialListData();
    closeList();
  };

  return (
    <>
      {operationConfigArr.map((item) => {
        const isFeedOperation = item.name === 'Feed';

        let disabled = isFeedOperation
          ? false
          : !isInIsland && ![PetStatus.IDLE, PetStatus.FOLLOW].includes(item.action);

        return (
          <PetOperationItem
            key={item.name}
            $active={petItem.petStatus === item.action}
            $loading={isChangePetStatusLoading}
            $disabled={disabled}
            onClick={(e) => {
              e.preventDefault();
              if (disabled) return;
              if (isFeedOperation) {
                handleFeed();
                return;
              }
              if (petItem.petStatus !== item.action) {
                handleChangePetStatus(item.action);
              }
            }}>
            <StyledOperationSvg>
              <SpriteSvg id={item.iconId} />
            </StyledOperationSvg>
            <span>{item.name}</span>
          </PetOperationItem>
        );
      })}
    </>
  );
};

const StyledStatusSvg = styled(SvgWrapper)`
  height: 1.25rem;
  flex-shrink: 0;
`;

const StyledStatusTag = styled(StatusTag)`
  min-width: 4.625rem;
  height: 1.25rem;
  & > span:last-of-type {
    padding: 0;
    & > span {
      font-size: 0.6875rem;
    }
  }
`;

const PetStatusPanel = () => {
  const petList = useAppSelector((state) => state.GameReducer.petList);
  const currentSummonList = useMemo(() => {
    return petList
      .filter((item) => item.petStatus !== PetStatus.REST)
      .sort((a, b) => (a.lastSummonedAt < b.lastSummonedAt ? -1 : 1));
  }, [petList]);

  const contextDispatch = useContextDispatch();
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const isExpand = useContextSelector((state) => state.isExpand);
  const isPetFeedModalOpen = useContextSelector((state) => state.isPetFeedModalOpen);

  const handleClick = (item: IBagPetList) => {
    if (isPetFeedModalOpen) return;

    if (isExpand && item._id === selectedPetId) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: '',
          selectedPetInfo: {},
          isExpand: false,
        },
      });
    } else {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedPetId: item._id,
          selectedPetInfo: item,
          isExpand: true,
        },
      });
    }
  };

  const statusIconArr = [...basicOperationArr, ...featureOperationConfigArr];

  return (
    <PetStatusList>
      <AnimatePresence>
        {currentSummonList.map((item, idx) => {
          const active = selectedPetId === item._id;
          const percent = (item.currentStamina / item.stamina) * 100;
          let color = staminaColorConfigArr[0].color;
          for (let index = 0; index < staminaColorConfigArr.length; index++) {
            const item = staminaColorConfigArr[index];
            if (percent >= item.limit) {
              color = item.color;
              break;
            }
          }

          const currentOperation =
            statusIconArr.find((it) => it.action === item.petStatus) || statusIconArr[0];

          const isSelectedItem = item._id === selectedPetId;

          return (
            <PetStatusItem
              key={'petStatus' + item._id}
              initial={{ opacity: 0, scale: 0.3 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.7 }}
              transition={{
                duration: 0.4,
                ease: 'easeOut',
                scale: {
                  type: 'spring',
                  stiffness: 300,
                  damping: 10,
                },
              }}>
              <CursorBtn petItem={item} />
              <PetImageStaminBox
                onClick={(e) => {
                  e.preventDefault();
                  handleClick(item);
                }}
                style={isPetFeedModalOpen && isSelectedItem ? { zIndex: 14 } : {}}>
                <PetStatusImageBox $active={active}>
                  <Image
                    width={512}
                    height={512}
                    src={item.bagConfigInfo.infoImageUrl}
                    loading="lazy"
                    alt="pet image"
                    draggable={false}
                  />
                  <StyledStatusTag
                    $borderColor={currentOperation.panelColorConfig.borderColor}
                    $color={currentOperation.panelColorConfig.color}>
                    <StyledStatusSvg>
                      <SpriteSvg id="petStatus" />
                    </StyledStatusSvg>
                    <span>
                      <span data-text={currentOperation.panelDisplay}>
                        {currentOperation.panelDisplay}
                      </span>
                    </span>
                  </StyledStatusTag>
                </PetStatusImageBox>
                <PetStamina $percent={isNaN(percent) ? 0 : percent} $bgColor={color} />
              </PetImageStaminBox>
            </PetStatusItem>
          );
        })}
      </AnimatePresence>
    </PetStatusList>
  );
};

export default PetStatusPanel;

const CursorBtn = ({ petItem }: { petItem: IBagPetList }) => {
  const selectedPetId = useContextSelector((state) => state.selectedPetId);
  const contextDispatch = useContextDispatch();
  const active = selectedPetId === petItem._id;
  const [showList, setShowList] = useState(false);

  const cursorRef = useRef<HTMLSpanElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const handleCursorClick = (item: IBagPetList) => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        selectedPetId: item._id,
        selectedPetInfo: item,
      },
    });
    setShowList(true);
  };

  useClickAway(() => {
    setShowList(false);
  }, [cursorRef, listRef]);

  const closeList = useCallback(() => {
    setShowList(false);
  }, []);

  return (
    <CursorBtnContainer>
      <StyledCursor
        ref={cursorRef}
        $active={active}
        onClick={(e) => {
          e.preventDefault();
          handleCursorClick(petItem);
        }}>
        <SpriteSvg id="cursor" />
      </StyledCursor>
      <PetOperationListWrapper ref={listRef}>
        <AnimatePresence>
          {active && showList && (
            <PetOperationList
              ref={listRef}
              key={petItem._id + 'operationListAnimate'}
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              exit={{
                opacity: 0,
              }}
              transition={{
                duration: 0.3,
                delay: 0.1,
                ease: 'easeInOut',
              }}>
              <OperationList petItem={petItem} closeList={closeList} />
            </PetOperationList>
          )}
        </AnimatePresence>
      </PetOperationListWrapper>
    </CursorBtnContainer>
  );
};
