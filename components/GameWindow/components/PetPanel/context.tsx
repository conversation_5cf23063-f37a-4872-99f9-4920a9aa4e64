import { IBagPetList } from '@/constant/type';
import { createTemplateContext } from '@/utils/createTemplateContext';

interface PetPanelContext {
  isExpand: boolean;
  selectedPetId: string;
  selectedPetInfo: Partial<IBagPetList>;
  onDetailPanelClose: () => void;
  isChangePetStatusLoading: boolean;
  isRenameModalOpen: boolean;
  isReleaseModalOpen: boolean;
  isPetFeedModalOpen: boolean;
  petFoodConfigList: { tag: string; stamina: number }[];
}

const initState: PetPanelContext = {
  isExpand: false,
  selectedPetId: '',
  selectedPetInfo: {},
  onDetailPanelClose: () => false,
  isChangePetStatusLoading: false,
  isRenameModalOpen: false,
  isReleaseModalOpen: false,
  isPetFeedModalOpen: false,
  petFoodConfigList: [],
};

const {
  TemplateContextProvider: PetPanelContextProvider,
  useContextDispatch,
  useContextSelector,
} = createTemplateContext<PetPanelContext>(initState);
export { PetPanelContextProvider, useContextDispatch, useContextSelector };
