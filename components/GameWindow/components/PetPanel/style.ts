import styled from 'styled-components';
import { motion } from 'framer-motion';
export const PetDetailContainer = styled(motion.div)`
  display: flex;
  padding: 1rem;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 0.625rem;
  border-radius: 2rem;
  background: #fff2e2;
  box-shadow:
    0 0.25rem 0.25rem 0 rgba(0, 0, 0, 0.25),
    0.125rem 0.125rem 0.25rem 0 rgba(255, 255, 255, 0.6) inset;
  box-sizing: border-box;
  width: 27.125rem;
  min-height: 41rem;

  position: fixed;
  flex-shrink: 0;
  z-index: 12;
  right: 3.33%;
  top: 12rem;

  box-shadow:
    0 0.25rem 0.25rem 0 rgba(0, 0, 0, 0.25),
    0.125rem 0.125rem 0.25rem 0 rgba(255, 255, 255, 0.6) inset,
    0 0 0 0.25rem #ed9800 inset;

  * {
    box-sizing: border-box;
  }
`;
export const PetStatusPanelContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.625rem;
  border-radius: 2rem;
  box-sizing: border-box;
  width: 7rem;

  position: fixed;
  flex-shrink: 0;
  z-index: 12;
  right: 4rem;
  margin: auto 0;
  top: 12rem;
  * {
    box-sizing: border-box;
    user-select: none;
  }
`;
