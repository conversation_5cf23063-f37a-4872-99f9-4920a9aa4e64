import { AnimatePresence, motion, useAnimate } from 'framer-motion';
import styled from 'styled-components';
import { ChatTabType } from '@/game/TS/Chat/ChatType';
import { SpriteSvg } from '@/components/SvgWrapper';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ChannelMenu from './components/ChannelMenu';
import {
  ChatRoomContainer,
  ChatRoomPreviewContainer,
  MainMessageContainer,
  ReplyBoxContainer,
  RightBox,
  StyledSvgWrapper,
} from './styles';
import { ChatManager } from '@/game/TS/Chat/ChatManager';
import { ChatListener } from '@/game/TS/Chat/ChatListener';
import { ChatEvent } from '@/game/TS/Chat/ChatEvent';
import { ChatRoomContextProvider, useContextDispatch, useContextSelector } from './context';
import Toolbar from './components/Toolbar';
import MessageList, { IMessageListRef } from './components/MessageList';
import ReplyBox from './components/ReplyBox';
import MessageInputBox, { ChatRoomInputRef } from './components/MessageInputBox';
import AirdropModal, { AirdropModalRef } from '@/components/ChatRoom/components/AirdropModal';
import ChatRoomPreview from './components/ChatRoomPreview';
import { useAppSelector } from '@/hooks/useStore';
import { KeyPressUtil } from '@/game/Global/GlobalKeyPressUtil';

const StyledButton = styled.button`
  display: flex;
  width: 7.5rem;
  height: 2.5rem;
  padding: 0.763875rem;
  justify-content: center;
  align-items: center;
  gap: 0.763875rem;
  border-radius: 1.22225rem;
  background: linear-gradient(180deg, #ff8316 0%, #fe6a00 100%);
  box-shadow:
    0rem 0.25rem 0rem 0rem rgba(0, 0, 0, 0.25),
    0rem 0.125rem 0.125rem 0rem rgba(255, 255, 255, 0.25) inset,
    0rem -0.25rem 0rem 0rem rgba(0, 0, 0, 0.25) inset;

  color: #fff;
  font-family: 'JetBrains Mono';
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;

  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  margin: 0 auto;
  white-space: nowrap;
  transition:
    transform 0.1s ease,
    box-shadow 0.1s ease;

  &:active {
    transform: translateY(0.125rem);
    box-shadow:
      0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.25),
      0rem 0.0625rem 0.0625rem 0rem rgba(255, 255, 255, 0.25) inset,
      0rem -0.125rem 0rem 0rem rgba(0, 0, 0, 0.25) inset;
  }
`;

const NewMessageBtn: FC<{
  onClick: () => void;
}> = ({ onClick = () => false }) => {
  return (
    <StyledButton onClick={onClick}>
      <span>New Message</span>
    </StyledButton>
  );
};

const ChatRoom = () => {
  const [containerRef, containerAnimate] = useAnimate();
  const [isOpen, setIsOpen] = useState(false);

  const contextDispatch = useContextDispatch();
  const typeList = useContextSelector((state) => state.typeList);
  const selectedTab = useContextSelector((state) => state.selectedTab);
  const replyChatData = useContextSelector((state) => state.replyChatData);
  const showNewMessageButton = useContextSelector((state) => state.showNewMessageButton);
  const airdropModalRef = useRef<AirdropModalRef>(null);
  const inputRef = useRef<ChatRoomInputRef>(null);
  const scrollRef = useRef<IMessageListRef>(null);
  const isMobile = useAppSelector((state) => state.AppReducer.isMobile);
  const onExpand = useCallback(() => {
    setIsOpen(true);
  }, []);

  const handleReplyBtnClick = () => {
    inputRef.current?.focusInput?.();
  };

  useEffect(() => {
    const chatTypeChange = () => {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          typeList: ChatManager.getInstance().getChatTypes(),
        },
      });
    };
    ChatListener.getInstance().addListener(ChatEvent.ChatTypeChange, chatTypeChange);
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.ChatTypeChange, chatTypeChange);
    };
  }, []);

  useEffect(() => {
    contextDispatch({
      type: 'UPDATE',
      payload: {
        replyChatData: null,
      },
    });

    const handleNewMessage = (tabType: ChatTabType) => {
      if (tabType === selectedTab) {
        const chatList = ChatManager.getInstance().getChatList(selectedTab);
        if (chatList) {
          chatList.clearNewMessageCount();
          const getChatMessages = chatList.getChatDataList();
          const filteredMessages = getChatMessages.filter(
            (msg) => msg.content && msg.content.trim() !== ''
          );
          contextDispatch({ type: 'UPDATE', payload: { messages: filteredMessages } });
        } else {
          contextDispatch({ type: 'UPDATE', payload: { messages: [] } });
        }
      }
    };

    handleNewMessage(selectedTab);

    ChatListener.getInstance().addListener(ChatEvent.ReceiveChat, handleNewMessage);
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.ReceiveChat, handleNewMessage);
    };
  }, [selectedTab]);

  useEffect(() => {
    if (containerRef.current) {
      if (isOpen) {
        containerAnimate(
          containerRef.current,
          {
            opacity: 1,
            transform: 'translateX(0)',
          },
          { duration: 0.3, ease: 'circIn' }
        );
      } else {
        containerAnimate(
          containerRef.current,
          {
            opacity: 0,
            transform: 'translateX(-120%)',
          },
          { duration: 0.3, ease: 'circOut' }
        );
      }
    }
  }, [isOpen]);

  useEffect(() => {
    if (typeList.length > 0 && selectedTab === -1) {
      contextDispatch({
        type: 'UPDATE',
        payload: {
          selectedTab: typeList[0],
        },
      });
    }
  }, [selectedTab, typeList]);

  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(['Enter'], (_event: KeyboardEvent) => {
      setIsOpen(true);
    });
    return () => {
      cancel();
    };
  }, []);

  if (!typeList || typeList?.length === 0) {
    return null;
  }

  return (
    <>
      <ChatRoomPreviewContainer
        style={{
          opacity: isOpen ? 0 : 1,
        }}
        $isMobile={isMobile}>
        <ChatRoomPreview onExpand={onExpand} />
      </ChatRoomPreviewContainer>
      <ChatRoomContainer
        ref={containerRef}
        initial={{
          opacity: 0,
          transform: 'translateX(-120%)',
        }}
        onContextMenu={(e) => {
          e.preventDefault();
        }}>
        <StyledSvgWrapper
          onClick={() => {
            setIsOpen(false);
          }}
          $expand={isOpen}>
          <SpriteSvg id="expandArrow" />
        </StyledSvgWrapper>
        <ChannelMenu
          scrollToBottom={() => {
            scrollRef.current?.scrollToBottom();
          }}
        />

        <RightBox>
          <Toolbar />
          <MainMessageContainer>
            <MessageList ref={scrollRef} handleReplyFocus={handleReplyBtnClick} />
            {showNewMessageButton && (
              <NewMessageBtn
                onClick={() => {
                  scrollRef.current?.scrollToBottom();
                }}
              />
            )}
          </MainMessageContainer>
          <AnimatePresence initial={false}>
            {!!replyChatData && (
              <ReplyBoxContainer
                key="replyBoxMotion"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0 }}>
                <ReplyBox />
              </ReplyBoxContainer>
            )}
          </AnimatePresence>

          <MessageInputBox
            ref={inputRef}
            onAirdrop={() => {
              airdropModalRef.current?.open();
            }}
            onSend={() => {
              scrollRef.current?.scrollToBottom();
            }}
          />
        </RightBox>
      </ChatRoomContainer>

      <AirdropModal
        ref={airdropModalRef}
        onClose={() => {
          airdropModalRef.current?.close();
        }}
        setBirthdayButtonLoading={(loading) => {
          inputRef.current?.setIsLoading(loading);
        }}
      />
    </>
  );
};

const ChatRoomWithContext = () => {
  return (
    <ChatRoomContextProvider>
      <ChatRoom />
    </ChatRoomContextProvider>
  );
};

export default ChatRoomWithContext;
