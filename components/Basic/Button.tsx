import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';
import SvgWrapper, { SpriteSvg } from '../SvgWrapper';
import { ButtonHTMLAttributes } from 'react';

type ButtonType = 'primary' | 'cancel';
const BasicButton = styled.button<{ $type?: ButtonType; $loading?: boolean }>`
  display: flex;
  width: 12.5rem;
  height: 3.75rem;
  padding: 1.875rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 1rem;

  font-weight: 700;
  border-radius: 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  line-height: 100%;
  cursor: pointer;
  min-width: 11.25rem;
  border: none;
  color: white;
  text-transform: capitalize;
  ${({ $type = 'primary', $loading = false }) =>
    $type === 'primary'
      ? css`
          background: hsl(28.07deg 100% 54.31%);
          box-shadow: 0px -0.25rem 0px 0px rgba(0, 0, 0, 0.25) inset;
          position: relative;
          transition:
            transform 0.1s,
            box-shadow 0.1s,
            border-bottom 0.1s;

          &:hover {
            background: hsl(28.07deg 100% 59.31%);
            cursor: pointer;
          }

          &:not(:disabled):active {
            box-shadow: none;
            background: #fc7922;
          }

          ${$loading &&
          css`
            &::before {
              content: '';
              width: 1.5em;
              height: 1.5em;
              border: 0.3125rem solid #fff;
              border-bottom-color: transparent;
              border-radius: 50%;
              display: inline-block;
              box-sizing: border-box;
              animation: rotation 1s linear infinite;
            }
            cursor: wait;
            &:hover {
              cursor: wait;
            }
          `}
          &:disabled {
            background: #c9b7a5;
            cursor: not-allowed;
          }
        `
      : css`
          background: #c9b7a5;
        `}

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
type BtnType = 'primary' | 'green';

type ButtonWrapperType = {
  $isLoading?: boolean;
  $btnType?: BtnType;
};

const ButtonWrapper = styled.button<ButtonWrapperType>`
  cursor: ${(props) => (props.$isLoading ? 'not-allowed' : 'pointer')};
  margin: 0;
  display: flex;
  width: 11.75rem;
  height: 4rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 1rem;
  border: 0.0625rem solid #5b402d;
  background: #ff8316;
  box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
  color: #fff;
  font-family: 'JetBrains Mono';
  font-size: 1.25rem;
  font-style: normal;
  font-weight: 700;
  line-height: 100%;
  position: relative;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease,
    background 0.2s ease-in;
  box-sizing: border-box;

  ${({ $btnType = 'primary' }) => {
    switch ($btnType) {
      case 'green':
        return css`
          background: #007a83;
        `;
      default:
        return css`
          background: #ff8316;
        `;
    }
  }}

  &:not(:disabled):active {
    transform: translateY(0.125rem);
    box-shadow: 0 -0.125rem 0 0 rgba(0, 0, 0, 0.3) inset;
  }

  &:disabled {
    background: #c9b7a5;
    cursor: not-allowed;
  }

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.3125rem solid #fff;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
  }

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const HightLightWrapper = styled(SvgWrapper)`
  width: 4.625rem;
  height: 0.96875rem;
  position: absolute;
  left: 0.75rem;
  top: 0.375rem;
`;

type LargeButtonType = Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'children'> & {
  isLoading?: boolean;
  children?: React.ReactNode;
  btnType?: BtnType;
};

export const LargeButton: React.FC<LargeButtonType> = ({
  isLoading,
  children,
  btnType = 'primary',
  disabled,
  ...props
}) => {
  return (
    <ButtonWrapper {...props} $isLoading={isLoading} $btnType={btnType} disabled={disabled}>
      {!disabled && (
        <HightLightWrapper>
          <SpriteSvg id={btnType === 'primary' ? 'btnHightLight' : 'btnHightLight2'} />
        </HightLightWrapper>
      )}
      {isLoading ? <div className="loading-spinner" /> : children}
    </ButtonWrapper>
  );
};

export default BasicButton;
