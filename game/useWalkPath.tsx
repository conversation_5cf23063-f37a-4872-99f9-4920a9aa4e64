import { Entity } from '@/game/TS/Entity/Entity';
import { useThree } from '@react-three/fiber';
import { useCallback, useEffect, useMemo, useState } from 'react';
import * as THREE from 'three';
import { useControls } from 'leva';
import { SKETCH } from '@/game_lib/const';
import { Raycaster } from 'three/src/Three.Core';
import GlobalSpaceEvent, { GlobalDataKey } from '@/game/Global/GlobalSpaceEvent';
import { ComponentType } from '@/game/TS/Entity/Enum';
import { EntityWalkPoint } from '@/game/TS/Entity/Components/EntityWalkPoint';
import { useNavigation } from '@/game_lib/navigation/navigation';

const raycasterTemp = new Raycaster();
const findGroundPosition = (stopList: THREE.Object3D[], position: THREE.Vector3) => {
  // 从中心点到生成的点发射射线
  const startPoint = position.clone();
  startPoint.y += 2;
  raycasterTemp.set(startPoint, new THREE.Vector3(0, -1, 0));
  raycasterTemp.far = 4; //多检测1的长度 , 给出生点留出体积
  const intersects = raycasterTemp.intersectObjects(stopList);
  if (intersects.length > 0) {
    return intersects[0].point;
  }
  return position;
};

function useWolkPoint(entity: Entity) {
  const { scene } = useThree();
  const [sceneLoading, setSceneLoading] = useState<boolean>(true);

  const stopList = useMemo(() => {
    if (sceneLoading) return [];
    const list: THREE.Object3D[] = [];
    scene.traverse((value) => {
      if (value.type === 'Mesh' && value.name === 'stop') {
        list.push(value);
      }
    });
    return list;
  }, [scene, sceneLoading]);

  useEffect(() => {
    const loadingKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (value) => {
        setSceneLoading(value);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, loadingKey);
    };
  }, []);

  const startWalkPoint = useCallback(
    (position: THREE.Vector3, arriveCallback: (isTransform: boolean) => void) => {
      const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
      walkPoint?.setTarget(findGroundPosition(stopList, position));
      // walkPoint?.setTarget(path[pathIndexRef.current]);
      walkPoint?.setArriveCallback((isTransform: boolean) => {
        arriveCallback(isTransform);
      });
    },
    [entity, stopList]
  );

  return { startWalkPoint };
}

export default function useWalkPath({ entity }: { entity: Entity }) {
  const { scene } = useThree();
  const { navMeshQuery } = useNavigation();
  const [debugRoot, setDebugRoot] = useState<THREE.Object3D | null>(null);
  const [path, setPath] = useState<THREE.Vector3[]>([]);
  const [pathIndex, setPathIndex] = useState(0);
  const [target, setTarget] = useState<THREE.Vector3>(new THREE.Vector3());
  const { navMeshDebug } = useControls(`${SKETCH}-navigation`, {
    boundsDebug: false,
    navMeshDebug: false,
  });
  const { startWalkPoint } = useWolkPoint(entity);

  useEffect(() => {
    if (!navMeshDebug) return;
    const root = new THREE.Object3D();
    scene.add(root);
    setDebugRoot(root);
    return () => {
      scene.remove(root);
      setDebugRoot(null);
    };
  }, [scene, navMeshDebug]);

  useEffect(() => {
    if (navMeshDebug && debugRoot) {
      debugRoot.clear();
      const randomColor = Math.floor(Math.random() * 0xffffff);
      path.forEach((item) => {
        const ball = new THREE.Mesh(
          new THREE.SphereGeometry(0.1),
          new THREE.MeshBasicMaterial({ color: randomColor })
        );
        ball.position.copy(item);
        debugRoot.add(ball);
      });
    }
  }, [debugRoot, navMeshDebug, path]);

  useEffect(() => {
    if (path.length == 0 || pathIndex >= path.length) return;
    startWalkPoint(path[pathIndex], () => {
      setPathIndex(pathIndex + 1);
    });
  }, [path, pathIndex]);

  const setTargetPosition = (targetPosition: THREE.Vector3) => {
    const walkPoint = entity.getComponent<EntityWalkPoint>(ComponentType.WalkPoint);
    if (walkPoint && walkPoint.position && navMeshQuery) {
      const { point: start } = navMeshQuery.findClosestPoint(walkPoint.position, {
        halfExtents: { x: 5, y: 5, z: 5 },
      });
      const { path, success } = navMeshQuery.computePath(start, targetPosition, {
        halfExtents: { x: 2, y: 2, z: 2 },
      });
      const threePath = path.map((item) => new THREE.Vector3(item.x, item.y, item.z));
      if (threePath.length > 0) {
        const firstDistance = threePath[0].distanceTo(
          new THREE.Vector3(walkPoint.position.x, threePath[0].y, walkPoint.position.z)
        );
        if (firstDistance < 1) {
          threePath.shift();
        }
        const distance = threePath[threePath.length - 1].distanceTo(targetPosition);
        if (distance > 5) {
          setPath([targetPosition]);
        } else {
          setPath(threePath);
        }
      } else {
        setPath([targetPosition]);
      }
      setPathIndex(0);
      setTarget(targetPosition);
    }
  };

  return { path, pathIndex, target, setTargetPosition };
}
