import * as THREE from 'three';

const DEFAULT_TEXTUREURL = '/particles/dot.png';

export default class ParticleInstanceMesh {
  constructor(parent, particleCount = 1000) {
    this.maxCount = particleCount;
    this.poolUseIndex = 0;
    this.camera = null; // 存储摄像机引用

    const particleGeometry = new THREE.PlaneGeometry(0.5, 0.5);
    const colors = new Float32Array(particleCount * 3);
    this.colorAttribute = new THREE.InstancedBufferAttribute(colors, 3);
    particleGeometry.setAttribute('instanceColor', this.colorAttribute);

    // 尝试加载纹理，失败时使用程序化纹理
    const textureLoader = new THREE.TextureLoader();

    const texture = textureLoader.load(DEFAULT_TEXTUREURL, function (loadedTexture) {
      console.log('Particle texture loaded successfully');
    });
    //
    // const particleMaterial = new THREE.MeshBasicMaterial({
    //   map: texture,
    //   color: 0xc97024,
    //   transparent: true,
    //   side: THREE.FrontSide,
    //   depthWrite: false,
    //   blending: THREE.AdditiveBlending,
    // });
    const particleMaterial = new THREE.MeshLambertMaterial({
      map: texture,
      vertexColors: true,
      transparent: true,
      side: THREE.FrontSide,
      depthWrite: false,
      // opacity: 0.8,
      // blending: THREE.AdditiveBlending,
    });
    this.instancedMesh = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);
    // this.instancedMesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    //初始所有实例不可见 。  scale = 0
    for (let i = 0; i < particleCount; i++) {
      matrix.compose(position, quaternion, scale);
      this.instancedMesh.setMatrixAt(i, matrix);
    }

    this.instancedMesh.instanceMatrix.needsUpdate = true;
    parent.add(this.instancedMesh);
  }

  createInstance() {
    this.poolUseIndex++;
    this.poolUseIndex = this.poolUseIndex % this.maxCount;
    return this.poolUseIndex;
  }

  updateInstance(instanceIndex, position, quaternion, scale = 1) {
    const matrix = new THREE.Matrix4();
    const scaleVec = new THREE.Vector3();
    scaleVec.set(scale, scale, scale);
    matrix.compose(position, quaternion, scaleVec);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  destroyInstance(instanceIndex) {
    //将实例变成不可见
    const matrix = new THREE.Matrix4();
    const quaternion = new THREE.Quaternion();
    const position = new THREE.Vector3();
    const scale = new THREE.Vector3();
    matrix.compose(position, quaternion, scale);
    this.instancedMesh.setMatrixAt(instanceIndex, matrix);
    this.instancedMesh.instanceMatrix.needsUpdate = true;
  }

  setParticleColor(hexColor) {
    const color = parseInt(hexColor.substring(1), 16);
    const r = ((color >> 16) & 255) / 255;
    const g = ((color >> 8) & 255) / 255;
    const b = (color & 255) / 255;

    for (let i = 0; i < particleCount; i++) {
      this.colorAttribute.setXYZ(i, r, g, b);
    }
    this.colorAttribute.needsUpdate = true;
  }

  // 按索引设置粒子颜色
  setParticleColorByIndex(index, color) {
    if (index === 1){
      console.log('color', color.r, color.g, color.b);
    }
    this.colorAttribute.setXYZ(index, color.r, color.g, color.b);
    this.colorAttribute.needsUpdate = true;
  }

  destroy() {
    // this.instancedMesh.removeFromParent();
  }
}
