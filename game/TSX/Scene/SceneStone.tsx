/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { AppGameApiKey, GetMyPlayer } from '@/game/TSX/Character/MyPlayer';
import { useThree } from '@react-three/fiber';
import GlobalSpace, { GAME_OP_TYPE } from '@/game/Global/GlobalSpace';
import { GLTF } from 'three-stdlib';
import { UseGameState } from '@/game_lib/stores/useGame';
import { RapierRigidBody, RigidBody } from '@react-three/rapier';
import { getParticleSystem } from '@/game/TSX/Particles/ParticleSystem';
import AudioSystemComponent, { AudioSystem } from '@/game/Global/GlobalAudioSystem';
import { StoneConfig, StoneData } from '@/game/Config/StoneConfig';
import { ItemType } from '@/game/Config/ItemConfig';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';

export function CreateMesh({ gltf, isDead }: { gltf: GLTF; isDead: boolean }) {
  const ref = useRef<THREE.Group>(null);
  const refBody = useRef<RapierRigidBody>(null);
  const [airWall, setAirWall] = useState<THREE.Mesh | null>(null);

  useEffect(() => {
    if (ref.current) {
      const group = ref.current;
      gltf.scene.traverse((child) => {
        if (child.type.includes('Mesh')) {
          if (child.name.includes('stop')) {
            setAirWall(child as THREE.Mesh);
          } else {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        }
      });
      group.add(gltf.scene);
      // 跳跃动作加入数组
    }
  }, []);

  useEffect(() => {
    if (refBody.current && airWall) {
      if (isDead) {
        const target = new THREE.Vector3(0, 0, 0);
        airWall.getWorldPosition(target);
        target.y -= 2.5;
        refBody.current.setTranslation(target, false);
        // 平滑过渡
        // refBody.current.position.lerp(new THREE.Vector3(0, 0, 0), 0.1);
      } else {
        const target = new THREE.Vector3(0, 0, 0);
        airWall.getWorldPosition(target);
        const rigidBody = refBody.current;
        const timerList: any[] = [];
        for (let i = 0; i < 10; i++) {
          const offsetY = Math.min(0, -2 + i * 0.3);
          timerList.push(
            setTimeout(() => {
              rigidBody.setTranslation(
                new THREE.Vector3(target.x, target.y + offsetY, target.z),
                false
              );
            }, i * 50)
          );
          if (offsetY == 0) {
            break;
          }
        }

        return () => {
          timerList.forEach((timer) => {
            clearTimeout(timer);
          });
        };
      }
    }
  }, [isDead, airWall]);

  return (
    <group>
      {airWall && (
        <RigidBody type="fixed" colliders="trimesh" ref={refBody} ccd>
          <mesh
            geometry={airWall.geometry}
            material={airWall.material}
            castShadow={false}
            receiveShadow={false}
            position={[airWall.position.x, airWall.position.y, airWall.position.z]}
            rotation={[airWall.rotation.x, airWall.rotation.y, airWall.rotation.z]}
            scale={airWall.scale}
            userData={{ camExcludeCollision: true }}
          />
        </RigidBody>
      )}
      <group position={[0, 0, 0]} ref={ref} />
    </group>
  );
}

export default function SceneStone({ stoneId }: { stoneId: number }) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const debugHitTree = localStorage.getItem('debugHitTree') === 'true';
  const particleSystem = getParticleSystem();
  const { scene } = useThree();
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const stoneObject = StoneConfig.getInstance().getObject(stoneId);
  const [stoneData, setStoneData] = useState<StoneData | null>(null);
  const [showHitButton, setShowHitButton] = useState<boolean>(false);
  const [comboFail, setComboFail] = useState<boolean>(false);
  const [isDead, setIsDead] = useState<boolean>(true);
  const [gltf, setGltf] = useState<GLTF | null>(null);
  const [stoneMesh, setStoneMesh] = useState<THREE.Object3D | null>(null);
  const [smokeObject, setSmokeObject] = useState<THREE.Object3D | null>(null);
  const [isLogin, setIsLogin] = useState<boolean>(myPlayer.btcAddress.length > 0);
  const groupRef = useRef<THREE.Group>(null);
  useEffect(() => {
    StoneConfig.getInstance().getData(stoneId, (data) => {
      setStoneData(data);
      if (stoneObject.status == 'alive') {
        setIsDead(false);
      }
    });
  }, []);

  useEffect(() => {
    if (stoneMesh) {
      //为登录或者未死亡显示树木
      stoneMesh.visible = !isLogin || !isDead;
    }
  }, [isDead, stoneMesh, isLogin]);

  useEffect(() => {
    if (groupRef.current && stoneData) {
      const group = groupRef.current;
      const loader = new GLTFLoader();
      loader.load(getCdnLink(stoneData.glb_url), (_gltf) => {
        setGltf(_gltf as any);

        _gltf.scene.traverse((child) => {
          if (child.name.includes('mesh')) {
            setStoneMesh(child);
          }
          if (child.name.includes('_E')) {
            setSmokeObject(child);
          }
        });
        group.position.set(stoneData.position[0], stoneData.position[1], stoneData.position[2]);
        group.quaternion.setFromEuler(new THREE.Euler(0, stoneData.yawY || 0, 0));
      });
    }
  }, [stoneData]);

  useEffect(() => {
    if (myPlayer.UsePetInscriptionId.length > 0) {
      return;
    }

    if (!isDead && showHitButton && !comboFail && stoneData && stoneObject.hp > 0) {
      const opKey = GlobalSpace.addGameOp(
        GAME_OP_TYPE.Mining,
        () => {
          myPlayer.hitStone(
            (isCombo, damage) => {
              const delay = isCombo ? 600 : 1000;
              setTimeout(() => {
                AudioSystem.playAudio(
                  'scene_stone_' + stoneData.id,
                  StoneConfig.getInstance().getHitSound(stoneData),
                  () => {
                    return true;
                  }
                );
              }, delay);
              stoneObject.combo += 1;
              setCurAnimation('Action_14');
              if (isCombo) {
                setTimeout(() => {
                  setCurAnimation('Action_15');
                }, 1);
              }
              if (stoneObject.hp < damage) {
                setComboFail(true);
              }
            },
            (damage, position, quaternion) => {
              if (stoneObject.hp <= 0) {
                //放置重复请求
                return;
              }
              stoneObject.hp -= damage;
              particleSystem.addParticle(
                position,
                quaternion,
                './particles/Effect_stone_0.json',
                stoneData.hit_effect_scale,
                stoneData.hit_effect_during
              );
              particleSystem.addParticle(
                position,
                quaternion,
                './particles/Effect_stone_1.json',
                stoneData.hit_effect_scale,
                stoneData.hit_effect_during
              );
              if (stoneObject.combo > 1) {
                myPlayer.callAppApi(AppGameApiKey.showCombo, stoneObject.combo);
              }

              if (stoneObject.hp > 0) {
              } else {
                const userItemId = myPlayer.axeParams?.userItemId || '';
                setTimeout(() => {
                  stoneObject.status = 'dead';
                  myPlayer.callAppApi(AppGameApiKey.mining, stoneObject.tag, userItemId);
                }, stoneData.disappear_time);
                setTimeout(() => {
                  if (smokeObject) {
                    const worldPos = smokeObject.getWorldPosition(new THREE.Vector3());
                    particleSystem.addParticle(
                      worldPos,
                      smokeObject.quaternion,
                      './particles/Effect_smoke_stone.json',
                      stoneData.fall_effect_scale,
                      stoneData.fall_effect_during
                    );
                  }
                }, stoneData.fall_effect_delay);
                AudioSystem.playAudio(
                  'scene_stone_' + stoneData.id,
                  StoneConfig.getInstance().getFallSound(stoneData),
                  () => {
                    return true;
                  }
                );
              }
            },
            () => {
              setComboFail(false);
              stoneObject.combo = 0;
            },
            () => {
              setComboFail(true);
            },
            scene,
            stoneData
          );
          if (debugHitTree) {
            // 显示树半径
            const geometry = new THREE.SphereGeometry(stoneData.radius);
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 }); // 红色高亮
            const sphere = new THREE.Mesh(geometry, material);
            sphere.position.set(
              stoneData.position[0],
              stoneData.position[1] + 0.2,
              stoneData.position[2]
            );
            scene.add(sphere);

            setTimeout(() => {
              scene.remove(sphere);
            }, 2000);
          }
        },
        stoneData.id
      );

      return () => {
        GlobalSpace.removeGameOp(opKey);
      };
    }
  }, [showHitButton, comboFail, isDead]);

  useEffect(() => {
    setIsLogin(btcAddress.length > 0);
  }, [btcAddress]);

  useEffect(() => {
    if (stoneData) {
      const pointKey = 'stone_' + stoneId;
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(stoneData.position[0], stoneData.position[1], stoneData.position[2]),
        (distance) => {
          if (myPlayer.itemData && myPlayer.itemData.type === ItemType.Pickaxe) {
            if (distance > stoneData.range[0] && distance < stoneData.range[3]) {
              //计算角度
              const angle = Math.atan2(
                myPlayer.position.z - stoneData.position[2],
                myPlayer.position.x - stoneData.position[0]
              );
              if (
                StoneConfig.getInstance().checkInSector(
                  stoneData.sector_range_start,
                  stoneData.sector_range_length,
                  angle
                )
              ) {
                setShowHitButton(true);
              } else {
                setShowHitButton(false);
              }
            } else {
              setShowHitButton(false);
            }
          } else {
            setShowHitButton(false);
          }
        }
      );
    }
  }, [stoneData]);

  useEffect(() => {
    if (stoneData && stoneObject) {
      const interval = setInterval(() => {
        if (isDead && stoneObject.status === 'alive') {
          setIsDead(false);
          stoneObject.hp = stoneData.max_hp;
        }
        if (!isDead && stoneObject.status !== 'alive') {
          setIsDead(true);
          stoneObject.hp = 0;
        }
      }, 1000);
      return () => {
        clearInterval(interval);
      };
    }
  }, [stoneData, isDead, stoneObject]);

  return (
    <>
      <group ref={groupRef}>
        {gltf && <CreateMesh gltf={gltf} isDead={isDead && isLogin} />}
        <AudioSystemComponent _key={'scene_stone_' + stoneId} />
      </group>
    </>
  );
}
